# 微博评论爬虫

## 项目简介

本项目是一个专门用于爬取微博"重庆公交坠江事故"相关评论数据的爬虫工具。能够自动获取热门微博及其评论，并将数据保存为多种格式。

## 功能特点

- 🎯 **精准爬取**: 爬取指定话题的前20条热门微博
- 💬 **深度挖掘**: 每条微博爬取最多300条评论
- 📊 **数据丰富**: 获取用户信息、内容、时间、互动数据等
- 💾 **多格式输出**: 支持CSV、JSON、Excel三种格式
- 🛡️ **反爬虫措施**: 随机延时、User-Agent轮换等
- 🔄 **错误处理**: 完善的重试机制和异常处理
- 📈 **实时监控**: 详细的日志输出和进度显示

## 环境要求

- Python 3.7+
- Chrome浏览器
- 稳定的网络连接

## 安装步骤

1. **克隆项目**
```bash
git clone <项目地址>
cd weibo-spider
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **运行爬虫**
```bash
python main.py
```

## 使用方法

### 基本使用

```bash
# 使用默认参数运行
python main.py

# 显示帮助信息
python main.py help

# 测试功能
python main.py test
```

### 高级参数

```bash
# 自定义参数运行
python main.py --max-weibo 15 --max-comments 250 --target-total 4000

# 使用有界面模式（调试用）
python main.py --headless False

# 自定义搜索URL
python main.py --url "https://s.weibo.com/weibo?q=自定义关键词"
```

### 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--headless` | 是否使用无头浏览器模式 | True |
| `--max-weibo` | 最大微博数量 | 20 |
| `--max-comments` | 每条微博最大评论数 | 300 |
| `--target-total` | 目标总评论数 | 5000 |
| `--url` | 微博搜索URL | 重庆公交坠江事故 |

## 输出文件

爬取完成后，数据将保存在 `data` 目录下：

- `weibo_comments_YYYYMMDD_HHMMSS_weibo.csv` - 微博数据
- `weibo_comments_YYYYMMDD_HHMMSS_comments.csv` - 评论数据  
- `weibo_comments_YYYYMMDD_HHMMSS.csv` - 合并数据
- `weibo_comments_YYYYMMDD_HHMMSS.json` - JSON格式数据
- `weibo_comments_YYYYMMDD_HHMMSS.xlsx` - Excel格式数据

## 数据字段

### 微博数据字段
- `weibo_id`: 微博ID
- `user_name`: 用户名
- `user_id`: 用户ID
- `content`: 微博内容
- `publish_time`: 发布时间
- `like_count`: 点赞数
- `comment_count`: 评论数
- `repost_count`: 转发数
- `weibo_url`: 微博链接

### 评论数据字段
- `comment_id`: 评论ID
- `weibo_id`: 所属微博ID
- `user_name`: 评论用户名
- `user_id`: 评论用户ID
- `content`: 评论内容
- `publish_time`: 评论时间
- `like_count`: 点赞数
- `reply_count`: 回复数
- `parent_comment_id`: 父评论ID

## 注意事项

1. **首次运行**: 程序会自动下载ChromeDriver，请确保网络畅通
2. **运行时间**: 完整爬取约需30-60分钟，请耐心等待
3. **数据量**: 实际获取的数据量可能少于目标值（取决于实际评论数）
4. **网络要求**: 需要稳定的网络连接，建议在网络良好时运行
5. **合规使用**: 请遵守相关法律法规，仅用于学习研究目的

## 故障排除

### 常见问题

1. **ChromeDriver下载失败**
   - 检查网络连接
   - 手动下载ChromeDriver并放置在系统PATH中

2. **页面加载超时**
   - 检查网络连接
   - 增加超时时间设置

3. **数据获取不完整**
   - 微博可能存在反爬虫限制
   - 适当增加延时时间

4. **程序崩溃**
   - 查看错误日志
   - 检查依赖包版本

### 调试模式

```bash
# 使用有界面模式观察爬取过程
python main.py --headless False

# 测试少量数据
python main.py test
```

## 项目结构

```
weibo-spider/
├── main.py              # 主程序入口
├── weibo_spider.py      # 爬虫核心类
├── config.py            # 配置文件
├── utils.py             # 工具函数
├── data_processor.py    # 数据处理模块
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
└── data/               # 数据输出目录
```

## 技术栈

- **网页解析**: Selenium + BeautifulSoup
- **数据处理**: Pandas
- **文件输出**: CSV, JSON, Excel
- **反爬虫**: 随机延时, User-Agent轮换
- **错误处理**: 重试机制, 异常捕获

## 许可证

本项目仅供学习研究使用，请遵守相关法律法规。
