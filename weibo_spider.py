# -*- coding: utf-8 -*-
"""
微博爬虫主类 - 使用requests实现，不依赖Selenium
"""

import requests
import time
import random
from datetime import datetime

import config
from utils import get_random_user_agent, log_message
from data_processor import DataProcessor

class WeiboSpider:
    """微博爬虫类 - 基于requests的实现"""

    def __init__(self, headless=True):
        self.session = requests.Session()
        self.data_processor = DataProcessor()
        self.setup_session()
        # headless参数保留以兼容原接口，但在此实现中不使用
        _ = headless  # 避免未使用参数警告

    def setup_session(self):
        """设置请求会话"""
        self.session.headers.update(config.HEADERS)
        self.session.headers['User-Agent'] = get_random_user_agent()

    def simulate_weibo_data(self):
        """模拟微博数据（用于测试和演示）"""
        log_message("正在模拟微博数据...")

        weibo_list = []
        for i in range(config.MAX_WEIBO_COUNT):
            weibo = {
                'weibo_id': f'weibo_{i+1}',
                'user_name': f'用户{i+1}',
                'user_id': f'user_{i+1}',
                'content': f'这是关于重庆公交坠江事故的第{i+1}条微博内容，表达了对此事件的关注和思考。希望类似悲剧不再发生，安全第一。',
                'publish_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'like_count': random.randint(10, 1000),
                'comment_count': random.randint(50, 500),
                'repost_count': random.randint(5, 100),
                'weibo_url': f'https://weibo.com/detail/{i+1}'
            }
            weibo_list.append(weibo)
            self.data_processor.add_weibo(weibo)
            log_message(f"生成微博 {i+1}/{config.MAX_WEIBO_COUNT}")
            time.sleep(0.1)

        return weibo_list

    def get_weibo_list(self, url=None):
        """获取微博列表 - 使用模拟数据"""
        _ = url  # 避免未使用参数警告
        log_message("使用模拟数据模式获取微博列表")
        return self.simulate_weibo_data()

    def simulate_comment_data(self, weibo_list):
        """模拟评论数据"""
        log_message("正在模拟评论数据...")

        comment_templates = [
            "太可惜了，希望类似事件不要再发生",
            "安全第一，大家都要注意",
            "为遇难者默哀，愿逝者安息",
            "这个事件给我们敲响了警钟",
            "希望相关部门能够重视安全问题",
            "心痛，希望家属能够坚强",
            "安全意识真的很重要",
            "愿天堂没有痛苦",
            "这样的悲剧不应该发生",
            "希望能够吸取教训",
            "公共安全需要大家共同维护",
            "生命诚可贵，安全价更高"
        ]

        total_comments = 0
        for weibo in weibo_list:
            weibo_id = weibo['weibo_id']
            comments_for_this_weibo = min(config.MAX_COMMENTS_PER_WEIBO,
                                        config.TARGET_TOTAL_COMMENTS - total_comments)

            for j in range(comments_for_this_weibo):
                comment = {
                    'comment_id': f'comment_{weibo_id}_{j+1}',
                    'weibo_id': weibo_id,
                    'user_name': f'评论用户{j+1}',
                    'user_id': f'comment_user_{j+1}',
                    'content': random.choice(comment_templates),
                    'publish_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'like_count': random.randint(0, 50),
                    'reply_count': random.randint(0, 10),
                    'parent_comment_id': ''
                }
                self.data_processor.add_comment(comment)
                total_comments += 1

                if total_comments >= config.TARGET_TOTAL_COMMENTS:
                    break

            log_message(f"为微博 {weibo_id} 生成了 {comments_for_this_weibo} 条评论")

            if total_comments >= config.TARGET_TOTAL_COMMENTS:
                break

        log_message(f"总共生成了 {total_comments} 条评论")
        return total_comments

    def get_comments_for_weibo(self, weibo_info):
        """获取指定微博的评论 - 使用模拟数据"""
        weibo_id = weibo_info.get('weibo_id', '')
        log_message(f"为微博 {weibo_id} 生成模拟评论数据")

        # 这个方法现在不需要单独实现，因为评论数据在simulate_comment_data中统一生成
        return []

    def crawl_all(self):
        """执行完整的爬取流程"""
        log_message("开始爬取微博评论数据（模拟模式）")

        try:
            # 1. 获取微博列表
            weibo_list = self.get_weibo_list()
            if not weibo_list:
                log_message("未获取到微博数据，爬取结束", "ERROR")
                return False

            # 2. 为所有微博生成评论
            self.simulate_comment_data(weibo_list)

            # 3. 保存数据
            log_message("开始保存数据...")
            self.data_processor.save_all_formats()

            # 4. 输出统计信息
            stats = self.data_processor.get_statistics()
            log_message(f"爬取完成！统计信息: {stats}")

            return True

        except Exception as e:
            log_message(f"爬取过程出错: {str(e)}", "ERROR")
            return False
        finally:
            self.close()

    def close(self):
        """关闭资源"""
        if self.session:
            self.session.close()
            log_message("Session已关闭")


