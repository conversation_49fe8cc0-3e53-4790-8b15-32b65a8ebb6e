# -*- coding: utf-8 -*-
"""
微博爬虫主类
"""

import requests
import time
import random
import json
import re
from urllib.parse import urljoin, urlparse

# 尝试导入selenium相关包，如果失败则使用备用方案
try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    HAS_SELENIUM = True
except ImportError:
    HAS_SELENIUM = False

try:
    from webdriver_manager.chrome import ChromeDriverManager
    HAS_WEBDRIVER_MANAGER = True
except ImportError:
    HAS_WEBDRIVER_MANAGER = False

import config
from utils import (
    get_random_user_agent, random_delay, clean_text, extract_number,
    format_time, log_message, extract_weibo_id, is_valid_weibo_content
)
from data_processor import DataProcessor

class WeiboSpider:
    """微博爬虫类"""

    def __init__(self, headless=True):
        self.session = requests.Session()
        self.data_processor = DataProcessor()
        self.driver = None
        self.headless = headless
        self.setup_session()

    def setup_session(self):
        """设置请求会话"""
        self.session.headers.update(config.HEADERS)
        self.session.headers['User-Agent'] = get_random_user_agent()

    def setup_driver(self):
        """设置Selenium WebDriver"""
        if not HAS_SELENIUM:
            log_message("Selenium未安装，无法使用WebDriver", "ERROR")
            return False

        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={get_random_user_agent()}')

            # 禁用图片加载以提高速度
            prefs = {"profile.managed_default_content_settings.images": 2}
            chrome_options.add_experimental_option("prefs", prefs)

            if HAS_WEBDRIVER_MANAGER:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # 尝试使用系统PATH中的chromedriver
                self.driver = webdriver.Chrome(options=chrome_options)

            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.driver.set_page_load_timeout(config.PAGE_LOAD_TIMEOUT)

            log_message("WebDriver初始化成功")
            return True
        except Exception as e:
            log_message(f"WebDriver初始化失败: {str(e)}", "ERROR")
            log_message("请确保已安装Chrome浏览器和相关依赖包", "ERROR")
            return False

    def get_weibo_list(self, url=None):
        """获取微博列表"""
        if not url:
            url = config.WEIBO_SEARCH_URL

        if not self.driver:
            if not self.setup_driver():
                return []

        try:
            log_message(f"正在访问: {url}")
            self.driver.get(url)
            random_delay(2, 4)

            # 等待页面加载
            WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                EC.presence_of_element_located((By.CLASS_NAME, "card-wrap"))
            )

            weibo_list = []
            cards = self.driver.find_elements(By.CLASS_NAME, "card-wrap")

            log_message(f"找到 {len(cards)} 个卡片元素")

            for i, card in enumerate(cards[:config.MAX_WEIBO_COUNT]):
                try:
                    weibo_info = self.parse_weibo_card(card)
                    if weibo_info and is_valid_weibo_content(weibo_info.get('content', '')):
                        weibo_list.append(weibo_info)
                        self.data_processor.add_weibo(weibo_info)
                        log_message(f"解析微博 {i+1}/{config.MAX_WEIBO_COUNT}: {weibo_info.get('user_name', 'Unknown')}")

                    random_delay(0.5, 1)
                except Exception as e:
                    log_message(f"解析微博卡片失败: {str(e)}", "ERROR")
                    continue

            log_message(f"成功获取 {len(weibo_list)} 条微博")
            return weibo_list

        except TimeoutException:
            log_message("页面加载超时", "ERROR")
            return []
        except Exception as e:
            log_message(f"获取微博列表失败: {str(e)}", "ERROR")
            return []

    def parse_weibo_card(self, card):
        """解析微博卡片信息"""
        try:
            weibo_info = {}

            # 用户信息
            user_element = card.find_element(By.CSS_SELECTOR, ".name")
            weibo_info['user_name'] = clean_text(user_element.text) if user_element else ""

            # 用户链接和ID
            try:
                user_link = user_element.find_element(By.TAG_NAME, "a").get_attribute("href")
                weibo_info['user_id'] = extract_weibo_id(user_link)
            except:
                weibo_info['user_id'] = ""

            # 微博内容
            try:
                content_element = card.find_element(By.CSS_SELECTOR, ".txt")
                weibo_info['content'] = clean_text(content_element.text)
            except:
                weibo_info['content'] = ""

            # 发布时间
            try:
                time_element = card.find_element(By.CSS_SELECTOR, ".from")
                weibo_info['publish_time'] = format_time(clean_text(time_element.text))
            except:
                weibo_info['publish_time'] = ""

            # 互动数据
            try:
                actions = card.find_elements(By.CSS_SELECTOR, ".card-act li")
                weibo_info['like_count'] = 0
                weibo_info['comment_count'] = 0
                weibo_info['repost_count'] = 0

                for action in actions:
                    text = action.text.lower()
                    if '赞' in text:
                        weibo_info['like_count'] = extract_number(text)
                    elif '评论' in text:
                        weibo_info['comment_count'] = extract_number(text)
                    elif '转发' in text:
                        weibo_info['repost_count'] = extract_number(text)
            except:
                pass

            # 微博链接和ID
            try:
                weibo_link = card.find_element(By.CSS_SELECTOR, ".from a").get_attribute("href")
                weibo_info['weibo_url'] = weibo_link
                weibo_info['weibo_id'] = extract_weibo_id(weibo_link)
            except:
                weibo_info['weibo_url'] = ""
                weibo_info['weibo_id'] = f"weibo_{int(time.time())}_{random.randint(1000, 9999)}"

            return weibo_info

        except Exception as e:
            log_message(f"解析微博卡片失败: {str(e)}", "ERROR")
            return None

    def get_comments_for_weibo(self, weibo_info):
        """获取指定微博的评论"""
        weibo_url = weibo_info.get('weibo_url', '')
        weibo_id = weibo_info.get('weibo_id', '')

        if not weibo_url:
            log_message(f"微博URL为空，跳过评论爬取: {weibo_id}", "WARNING")
            return []

        try:
            # 构建评论页面URL
            comment_url = weibo_url.replace('/detail/', '/comment/')
            if '?' not in comment_url:
                comment_url += '?'
            else:
                comment_url += '&'
            comment_url += 'type=comment'

            log_message(f"正在获取微博评论: {weibo_id}")
            self.driver.get(comment_url)
            random_delay(2, 3)

            comments = []
            comment_count = 0
            page = 1

            while comment_count < config.MAX_COMMENTS_PER_WEIBO and page <= 10:  # 最多翻10页
                try:
                    # 等待评论加载
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "list_li"))
                    )

                    comment_elements = self.driver.find_elements(By.CLASS_NAME, "list_li")

                    for element in comment_elements:
                        if comment_count >= config.MAX_COMMENTS_PER_WEIBO:
                            break

                        comment_info = self.parse_comment_element(element, weibo_id)
                        if comment_info:
                            comments.append(comment_info)
                            comment_count += 1

                    # 尝试点击下一页
                    try:
                        next_button = self.driver.find_element(By.CSS_SELECTOR, ".next")
                        if next_button.is_enabled():
                            next_button.click()
                            random_delay(2, 3)
                            page += 1
                        else:
                            break
                    except:
                        break

                except TimeoutException:
                    log_message("评论加载超时，尝试继续", "WARNING")
                    break
                except Exception as e:
                    log_message(f"获取评论页面失败: {str(e)}", "ERROR")
                    break

            log_message(f"成功获取 {len(comments)} 条评论 (微博ID: {weibo_id})")
            self.data_processor.add_comments_batch(comments)
            return comments

        except Exception as e:
            log_message(f"获取微博评论失败: {str(e)}", "ERROR")
            return []

    def parse_comment_element(self, element, weibo_id):
        """解析评论元素"""
        try:
            comment_info = {}
            comment_info['weibo_id'] = weibo_id

            # 用户名
            try:
                user_element = element.find_element(By.CSS_SELECTOR, ".name")
                comment_info['user_name'] = clean_text(user_element.text)

                # 用户ID
                user_link = user_element.get_attribute("href")
                comment_info['user_id'] = extract_weibo_id(user_link) if user_link else ""
            except:
                comment_info['user_name'] = ""
                comment_info['user_id'] = ""

            # 评论内容
            try:
                content_element = element.find_element(By.CSS_SELECTOR, ".txt")
                comment_info['content'] = clean_text(content_element.text)
            except:
                comment_info['content'] = ""

            # 发布时间
            try:
                time_element = element.find_element(By.CSS_SELECTOR, ".from")
                comment_info['publish_time'] = format_time(clean_text(time_element.text))
            except:
                comment_info['publish_time'] = ""

            # 点赞数
            try:
                like_element = element.find_element(By.CSS_SELECTOR, ".praise")
                comment_info['like_count'] = extract_number(like_element.text)
            except:
                comment_info['like_count'] = 0

            # 回复数
            try:
                reply_element = element.find_element(By.CSS_SELECTOR, ".reply")
                comment_info['reply_count'] = extract_number(reply_element.text)
            except:
                comment_info['reply_count'] = 0

            # 评论ID
            try:
                comment_id = element.get_attribute("comment-id")
                comment_info['comment_id'] = comment_id if comment_id else f"comment_{int(time.time())}_{random.randint(1000, 9999)}"
            except:
                comment_info['comment_id'] = f"comment_{int(time.time())}_{random.randint(1000, 9999)}"

            # 父评论ID（如果是回复）
            comment_info['parent_comment_id'] = ""

            return comment_info

        except Exception as e:
            log_message(f"解析评论元素失败: {str(e)}", "ERROR")
            return None

    def crawl_all(self):
        """执行完整的爬取流程"""
        log_message("开始爬取微博评论数据")

        try:
            # 1. 获取微博列表
            weibo_list = self.get_weibo_list()
            if not weibo_list:
                log_message("未获取到微博数据，爬取结束", "ERROR")
                return False

            # 2. 为每条微博获取评论
            total_comments = 0
            for i, weibo in enumerate(weibo_list):
                log_message(f"正在处理第 {i+1}/{len(weibo_list)} 条微博")

                comments = self.get_comments_for_weibo(weibo)
                total_comments += len(comments)

                log_message(f"当前总评论数: {total_comments}")

                # 检查是否达到目标数量
                if total_comments >= config.TARGET_TOTAL_COMMENTS:
                    log_message(f"已达到目标评论数量 {config.TARGET_TOTAL_COMMENTS}，停止爬取")
                    break

                random_delay(1, 2)

            # 3. 保存数据
            log_message("开始保存数据...")
            self.data_processor.save_all_formats()

            # 4. 输出统计信息
            stats = self.data_processor.get_statistics()
            log_message(f"爬取完成！统计信息: {stats}")

            return True

        except Exception as e:
            log_message(f"爬取过程出错: {str(e)}", "ERROR")
            return False
        finally:
            self.close()

    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
            log_message("WebDriver已关闭")

        if self.session:
            self.session.close()
            log_message("Session已关闭")
