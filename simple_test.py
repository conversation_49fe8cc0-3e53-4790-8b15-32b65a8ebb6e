# -*- coding: utf-8 -*-
"""
简化测试版本 - 检查基本功能
"""

import sys
import os

def test_imports():
    """测试导入模块"""
    print("🔍 测试模块导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup 导入成功")
    except ImportError as e:
        print(f"❌ BeautifulSoup 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ pandas 导入成功")
    except ImportError as e:
        print(f"❌ pandas 导入失败: {e}")
        return False
    
    try:
        from selenium import webdriver
        print("✅ selenium 导入成功")
    except ImportError as e:
        print(f"❌ selenium 导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置文件"""
    print("\n🔧 测试配置文件...")
    
    try:
        import config
        print(f"✅ 目标URL: {config.WEIBO_SEARCH_URL}")
        print(f"✅ 最大微博数: {config.MAX_WEIBO_COUNT}")
        print(f"✅ 每条微博最大评论数: {config.MAX_COMMENTS_PER_WEIBO}")
        print(f"✅ 目标总评论数: {config.TARGET_TOTAL_COMMENTS}")
        return True
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_utils():
    """测试工具函数"""
    print("\n🛠️ 测试工具函数...")
    
    try:
        from utils import clean_text, extract_number, log_message
        
        # 测试文本清理
        test_text = "  这是一个测试文本\n\t  "
        cleaned = clean_text(test_text)
        print(f"✅ 文本清理: '{test_text}' -> '{cleaned}'")
        
        # 测试数字提取
        test_num = "123万"
        number = extract_number(test_num)
        print(f"✅ 数字提取: '{test_num}' -> {number}")
        
        # 测试日志
        log_message("这是一条测试日志")
        print("✅ 日志功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False

def test_data_processor():
    """测试数据处理器"""
    print("\n📊 测试数据处理器...")
    
    try:
        from data_processor import DataProcessor
        
        processor = DataProcessor()
        
        # 测试添加微博数据
        test_weibo = {
            'weibo_id': 'test_001',
            'user_name': '测试用户',
            'content': '这是一条测试微博',
            'like_count': 100
        }
        processor.add_weibo(test_weibo)
        
        # 测试添加评论数据
        test_comment = {
            'comment_id': 'comment_001',
            'weibo_id': 'test_001',
            'user_name': '评论用户',
            'content': '这是一条测试评论',
            'like_count': 10
        }
        processor.add_comment(test_comment)
        
        # 测试统计信息
        stats = processor.get_statistics()
        print(f"✅ 统计信息: {stats}")
        
        return True
    except Exception as e:
        print(f"❌ 数据处理器测试失败: {e}")
        return False

def test_chrome_driver():
    """测试Chrome驱动"""
    print("\n🌐 测试Chrome驱动...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        print("正在下载/检查ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        
        print("正在启动Chrome浏览器...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("正在测试网页访问...")
        driver.get("https://www.baidu.com")
        title = driver.title
        print(f"✅ 网页标题: {title}")
        
        driver.quit()
        print("✅ Chrome驱动测试成功")
        return True
        
    except Exception as e:
        print(f"❌ Chrome驱动测试失败: {e}")
        print("请确保已安装Chrome浏览器")
        return False

def test_network():
    """测试网络连接"""
    print("\n🌍 测试网络连接...")
    
    try:
        import requests
        
        # 测试基本网络连接
        response = requests.get("https://www.baidu.com", timeout=10)
        if response.status_code == 200:
            print("✅ 基本网络连接正常")
        else:
            print(f"⚠️ 网络连接异常，状态码: {response.status_code}")
        
        # 测试微博网站连接
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get("https://weibo.com", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ 微博网站连接正常")
        else:
            print(f"⚠️ 微博网站连接异常，状态码: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始系统测试...\n")
    
    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config),
        ("工具函数", test_utils),
        ("数据处理器", test_data_processor),
        ("网络连接", test_network),
        ("Chrome驱动", test_chrome_driver),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📋 测试结果汇总:")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！系统准备就绪，可以开始爬取数据。")
        print("\n运行命令: python main.py")
    else:
        print(f"\n⚠️ 有 {len(results) - passed} 项测试失败，请检查环境配置。")
        
        if not results[0][1]:  # 模块导入失败
            print("\n💡 建议:")
            print("1. 运行: pip install -r requirements.txt")
            print("2. 确保Python版本 >= 3.7")
        
        if not results[-1][1]:  # Chrome驱动失败
            print("\n💡 建议:")
            print("1. 确保已安装Chrome浏览器")
            print("2. 检查网络连接")

if __name__ == "__main__":
    main()
