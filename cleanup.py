# -*- coding: utf-8 -*-
"""
项目清理脚本 - 删除无用文件和缓存
"""

import os
import shutil
import glob
from datetime import datetime

def remove_pycache():
    """删除Python缓存文件"""
    print("🗑️ 清理Python缓存文件...")
    
    # 删除__pycache__目录
    if os.path.exists("__pycache__"):
        shutil.rmtree("__pycache__")
        print("✅ 删除 __pycache__ 目录")
    
    # 删除.pyc文件
    pyc_files = glob.glob("**/*.pyc", recursive=True)
    for file in pyc_files:
        try:
            os.remove(file)
            print(f"✅ 删除 {file}")
        except:
            pass

def clean_old_data():
    """清理旧的数据文件（保留最新的2个）"""
    print("\n📁 清理旧数据文件...")
    
    if not os.path.exists("data"):
        print("📂 data目录不存在")
        return
    
    # 获取所有数据文件
    json_files = glob.glob("data/weibo_comments_*.json")
    csv_files = glob.glob("data/weibo_comments_*.csv")
    
    # 按修改时间排序，保留最新的2个
    for file_list, file_type in [(json_files, "JSON"), (csv_files, "CSV")]:
        if len(file_list) > 2:
            # 按修改时间排序
            file_list.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # 删除旧文件
            for old_file in file_list[2:]:
                try:
                    os.remove(old_file)
                    print(f"✅ 删除旧{file_type}文件: {os.path.basename(old_file)}")
                except:
                    print(f"❌ 无法删除: {old_file}")

def clean_temp_files():
    """删除临时文件"""
    print("\n🧹 清理临时文件...")
    
    temp_patterns = [
        "*.tmp",
        "*.temp", 
        "*.log",
        ".DS_Store",
        "Thumbs.db"
    ]
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"✅ 删除临时文件: {file}")
            except:
                pass

def show_project_structure():
    """显示清理后的项目结构"""
    print("\n📋 当前项目结构:")
    
    # 核心文件
    core_files = [
        "main.py",
        "simple_spider.py", 
        "weibo_spider.py",
        "config.py",
        "utils.py",
        "data_processor.py"
    ]
    
    # 配置文件
    config_files = [
        "requirements.txt",
        "README.md"
    ]
    
    # 辅助文件
    helper_files = [
        "install_dependencies.py",
        "simple_test.py",
        "cleanup.py"
    ]
    
    print("  📁 核心模块:")
    for file in core_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            print(f"    ✅ {file} ({size:.1f}KB)")
        else:
            print(f"    ❌ {file} (缺失)")
    
    print("\n  📁 配置文件:")
    for file in config_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            print(f"    ✅ {file} ({size:.1f}KB)")
    
    print("\n  📁 辅助工具:")
    for file in helper_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            print(f"    ✅ {file} ({size:.1f}KB)")
    
    # 数据文件
    if os.path.exists("data"):
        data_files = os.listdir("data")
        if data_files:
            print(f"\n  📁 数据文件 ({len(data_files)}个):")
            for file in sorted(data_files)[-3:]:  # 显示最新的3个
                filepath = os.path.join("data", file)
                size = os.path.getsize(filepath) / 1024
                print(f"    📄 {file} ({size:.1f}KB)")
            if len(data_files) > 3:
                print(f"    ... 还有 {len(data_files)-3} 个文件")

def main():
    """主清理函数"""
    print("🧹 开始清理项目...")
    print("=" * 50)
    
    # 执行清理
    remove_pycache()
    clean_old_data()
    clean_temp_files()
    
    print("\n" + "=" * 50)
    print("✅ 清理完成！")
    
    # 显示项目结构
    show_project_structure()
    
    print("\n💡 提示:")
    print("  • 运行 'python simple_spider.py' 测试基础功能")
    print("  • 运行 'python simple_test.py' 进行系统测试")
    print("  • 运行 'python main.py help' 查看完整帮助")

if __name__ == "__main__":
    main()
