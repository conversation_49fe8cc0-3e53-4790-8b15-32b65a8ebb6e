# -*- coding: utf-8 -*-
"""
数据处理和存储模块
"""

import pandas as pd
import json
import os
from datetime import datetime
import config
from utils import ensure_dir, log_message

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.weibo_data = []
        self.comment_data = []
        ensure_dir(config.OUTPUT_DIR)
    
    def add_weibo(self, weibo_info):
        """添加微博数据"""
        self.weibo_data.append(weibo_info)
        log_message(f"添加微博数据: {weibo_info.get('user_name', 'Unknown')} - {weibo_info.get('content', '')[:50]}...")
    
    def add_comment(self, comment_info):
        """添加评论数据"""
        self.comment_data.append(comment_info)
        log_message(f"添加评论数据: {comment_info.get('user_name', 'Unknown')} - {comment_info.get('content', '')[:30]}...")
    
    def add_comments_batch(self, comments_list):
        """批量添加评论数据"""
        self.comment_data.extend(comments_list)
        log_message(f"批量添加 {len(comments_list)} 条评论数据")
    
    def get_statistics(self):
        """获取数据统计信息"""
        stats = {
            'total_weibo': len(self.weibo_data),
            'total_comments': len(self.comment_data),
            'avg_comments_per_weibo': len(self.comment_data) / len(self.weibo_data) if self.weibo_data else 0
        }
        return stats
    
    def save_to_csv(self, filename=None):
        """保存为CSV文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"weibo_comments_{timestamp}.csv"
        
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            # 保存微博数据
            if self.weibo_data:
                weibo_df = pd.DataFrame(self.weibo_data)
                weibo_filepath = filepath.replace('.csv', '_weibo.csv')
                weibo_df.to_csv(weibo_filepath, index=False, encoding='utf-8-sig')
                log_message(f"微博数据已保存到: {weibo_filepath}")
            
            # 保存评论数据
            if self.comment_data:
                comment_df = pd.DataFrame(self.comment_data)
                comment_filepath = filepath.replace('.csv', '_comments.csv')
                comment_df.to_csv(comment_filepath, index=False, encoding='utf-8-sig')
                log_message(f"评论数据已保存到: {comment_filepath}")
            
            # 保存合并数据
            if self.weibo_data and self.comment_data:
                all_data = []
                for comment in self.comment_data:
                    # 找到对应的微博信息
                    weibo_info = next((w for w in self.weibo_data if w['weibo_id'] == comment['weibo_id']), {})
                    merged_data = {**weibo_info, **comment}
                    all_data.append(merged_data)
                
                if all_data:
                    all_df = pd.DataFrame(all_data)
                    all_df.to_csv(filepath, index=False, encoding='utf-8-sig')
                    log_message(f"合并数据已保存到: {filepath}")
            
            return filepath
        except Exception as e:
            log_message(f"保存CSV文件失败: {str(e)}", "ERROR")
            return None
    
    def save_to_json(self, filename=None):
        """保存为JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"weibo_comments_{timestamp}.json"
        
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            data = {
                'metadata': {
                    'crawl_time': datetime.now().isoformat(),
                    'total_weibo': len(self.weibo_data),
                    'total_comments': len(self.comment_data),
                    'target_url': config.WEIBO_SEARCH_URL
                },
                'weibo_data': self.weibo_data,
                'comment_data': self.comment_data
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            log_message(f"JSON数据已保存到: {filepath}")
            return filepath
        except Exception as e:
            log_message(f"保存JSON文件失败: {str(e)}", "ERROR")
            return None
    
    def save_to_excel(self, filename=None):
        """保存为Excel文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"weibo_comments_{timestamp}.xlsx"
        
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 保存微博数据
                if self.weibo_data:
                    weibo_df = pd.DataFrame(self.weibo_data)
                    weibo_df.to_excel(writer, sheet_name='微博数据', index=False)
                
                # 保存评论数据
                if self.comment_data:
                    comment_df = pd.DataFrame(self.comment_data)
                    comment_df.to_excel(writer, sheet_name='评论数据', index=False)
                
                # 保存统计信息
                stats = self.get_statistics()
                stats_df = pd.DataFrame([stats])
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            log_message(f"Excel数据已保存到: {filepath}")
            return filepath
        except Exception as e:
            log_message(f"保存Excel文件失败: {str(e)}", "ERROR")
            return None
    
    def save_all_formats(self):
        """保存所有格式的文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        results = {
            'csv': self.save_to_csv(f"weibo_comments_{timestamp}.csv"),
            'json': self.save_to_json(f"weibo_comments_{timestamp}.json"),
            'excel': self.save_to_excel(f"weibo_comments_{timestamp}.xlsx")
        }
        
        return results
    
    def clear_data(self):
        """清空数据"""
        self.weibo_data.clear()
        self.comment_data.clear()
        log_message("数据已清空")
    
    def export_summary(self):
        """导出数据摘要"""
        stats = self.get_statistics()
        
        summary = {
            'crawl_time': datetime.now().isoformat(),
            'statistics': stats,
            'sample_weibo': self.weibo_data[:3] if self.weibo_data else [],
            'sample_comments': self.comment_data[:10] if self.comment_data else []
        }
        
        return summary
