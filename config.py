# -*- coding: utf-8 -*-
"""
微博爬虫配置文件
"""

# 目标URL
WEIBO_SEARCH_URL = "https://s.weibo.com/weibo?q=%23%E9%87%8D%E5%BA%86%E5%85%AC%E4%BA%A4%E5%9D%A0%E6%B1%9F&xsort=hot&suball=1&Refer=g"

# 爬取配置
MAX_WEIBO_COUNT = 20  # 爬取微博数量
MAX_COMMENTS_PER_WEIBO = 300  # 每条微博的评论数量
TARGET_TOTAL_COMMENTS = 5000  # 目标总评论数

# 延时配置（秒）
MIN_DELAY = 1
MAX_DELAY = 3
COMMENT_DELAY = 0.5  # 评论间延时

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 5

# 输出配置
OUTPUT_DIR = "data"
CSV_FILENAME = "weibo_comments.csv"
JSON_FILENAME = "weibo_comments.json"
EXCEL_FILENAME = "weibo_comments.xlsx"

# User-Agent列表
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
]

# 请求头
HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# Selenium配置
SELENIUM_TIMEOUT = 10
IMPLICIT_WAIT = 5
PAGE_LOAD_TIMEOUT = 30

# 数据字段
WEIBO_FIELDS = [
    'weibo_id', 'user_name', 'user_id', 'content', 'publish_time', 
    'like_count', 'comment_count', 'repost_count', 'weibo_url'
]

COMMENT_FIELDS = [
    'comment_id', 'weibo_id', 'user_name', 'user_id', 'content', 
    'publish_time', 'like_count', 'reply_count', 'parent_comment_id'
]
