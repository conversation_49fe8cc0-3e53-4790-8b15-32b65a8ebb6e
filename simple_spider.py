# -*- coding: utf-8 -*-
"""
简化版微博爬虫 - 仅使用基本库
"""

import requests
import time
import random
import json
import os
from datetime import datetime
import config
from utils import get_random_user_agent, random_delay, clean_text, log_message, ensure_dir

class SimpleWeiboSpider:
    """简化版微博爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.data = {
            'weibo_data': [],
            'comment_data': []
        }
        self.setup_session()
        ensure_dir(config.OUTPUT_DIR)
    
    def setup_session(self):
        """设置请求会话"""
        self.session.headers.update(config.HEADERS)
        self.session.headers['User-Agent'] = get_random_user_agent()
    
    def simulate_weibo_data(self):
        """模拟微博数据（用于测试）"""
        log_message("正在模拟微博数据...")
        
        for i in range(config.MAX_WEIBO_COUNT):
            weibo = {
                'weibo_id': f'weibo_{i+1}',
                'user_name': f'用户{i+1}',
                'user_id': f'user_{i+1}',
                'content': f'这是关于重庆公交坠江事故的第{i+1}条微博内容，表达了对此事件的关注和思考。',
                'publish_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'like_count': random.randint(10, 1000),
                'comment_count': random.randint(50, 500),
                'repost_count': random.randint(5, 100),
                'weibo_url': f'https://weibo.com/detail/{i+1}'
            }
            self.data['weibo_data'].append(weibo)
            log_message(f"生成微博 {i+1}/{config.MAX_WEIBO_COUNT}")
            time.sleep(0.1)
    
    def simulate_comment_data(self):
        """模拟评论数据（用于测试）"""
        log_message("正在模拟评论数据...")
        
        comment_templates = [
            "太可惜了，希望类似事件不要再发生",
            "安全第一，大家都要注意",
            "为遇难者默哀，愿逝者安息",
            "这个事件给我们敲响了警钟",
            "希望相关部门能够重视安全问题",
            "心痛，希望家属能够坚强",
            "安全意识真的很重要",
            "愿天堂没有痛苦",
            "这样的悲剧不应该发生",
            "希望能够吸取教训"
        ]
        
        total_comments = 0
        for weibo in self.data['weibo_data']:
            weibo_id = weibo['weibo_id']
            comments_for_this_weibo = min(config.MAX_COMMENTS_PER_WEIBO, 
                                        config.TARGET_TOTAL_COMMENTS - total_comments)
            
            for j in range(comments_for_this_weibo):
                comment = {
                    'comment_id': f'comment_{weibo_id}_{j+1}',
                    'weibo_id': weibo_id,
                    'user_name': f'评论用户{j+1}',
                    'user_id': f'comment_user_{j+1}',
                    'content': random.choice(comment_templates),
                    'publish_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'like_count': random.randint(0, 50),
                    'reply_count': random.randint(0, 10),
                    'parent_comment_id': ''
                }
                self.data['comment_data'].append(comment)
                total_comments += 1
                
                if total_comments >= config.TARGET_TOTAL_COMMENTS:
                    break
            
            log_message(f"为微博 {weibo_id} 生成了 {comments_for_this_weibo} 条评论")
            
            if total_comments >= config.TARGET_TOTAL_COMMENTS:
                break
        
        log_message(f"总共生成了 {total_comments} 条评论")
    
    def save_to_json(self, filename=None):
        """保存为JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"weibo_comments_{timestamp}.json"
        
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            output_data = {
                'metadata': {
                    'crawl_time': datetime.now().isoformat(),
                    'total_weibo': len(self.data['weibo_data']),
                    'total_comments': len(self.data['comment_data']),
                    'target_url': config.WEIBO_SEARCH_URL,
                    'note': '这是模拟数据，用于测试爬虫功能'
                },
                'weibo_data': self.data['weibo_data'],
                'comment_data': self.data['comment_data']
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            log_message(f"JSON数据已保存到: {filepath}")
            return filepath
        except Exception as e:
            log_message(f"保存JSON文件失败: {str(e)}", "ERROR")
            return None
    
    def save_to_csv_simple(self, filename=None):
        """简单的CSV保存（不使用pandas）"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"weibo_comments_{timestamp}.csv"
        
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8-sig', newline='') as f:
                # 写入表头
                headers = ['类型', 'ID', '用户名', '用户ID', '内容', '发布时间', '点赞数', '评论数/回复数', '转发数', 'URL', '所属微博ID']
                f.write(','.join(headers) + '\n')
                
                # 写入微博数据
                for weibo in self.data['weibo_data']:
                    row = [
                        '微博',
                        weibo['weibo_id'],
                        weibo['user_name'],
                        weibo['user_id'],
                        f'"{weibo["content"]}"',  # 用引号包围内容
                        weibo['publish_time'],
                        str(weibo['like_count']),
                        str(weibo['comment_count']),
                        str(weibo['repost_count']),
                        weibo['weibo_url'],
                        ''
                    ]
                    f.write(','.join(row) + '\n')
                
                # 写入评论数据
                for comment in self.data['comment_data']:
                    row = [
                        '评论',
                        comment['comment_id'],
                        comment['user_name'],
                        comment['user_id'],
                        f'"{comment["content"]}"',  # 用引号包围内容
                        comment['publish_time'],
                        str(comment['like_count']),
                        str(comment['reply_count']),
                        '',
                        '',
                        comment['weibo_id']
                    ]
                    f.write(','.join(row) + '\n')
            
            log_message(f"CSV数据已保存到: {filepath}")
            return filepath
        except Exception as e:
            log_message(f"保存CSV文件失败: {str(e)}", "ERROR")
            return None
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'total_weibo': len(self.data['weibo_data']),
            'total_comments': len(self.data['comment_data']),
            'avg_comments_per_weibo': len(self.data['comment_data']) / len(self.data['weibo_data']) if self.data['weibo_data'] else 0
        }
    
    def run_simulation(self):
        """运行模拟爬取"""
        log_message("开始模拟微博评论爬取...")
        
        try:
            # 1. 模拟获取微博数据
            self.simulate_weibo_data()
            
            # 2. 模拟获取评论数据
            self.simulate_comment_data()
            
            # 3. 保存数据
            log_message("开始保存数据...")
            json_file = self.save_to_json()
            csv_file = self.save_to_csv_simple()
            
            # 4. 输出统计信息
            stats = self.get_statistics()
            log_message(f"模拟爬取完成！统计信息: {stats}")
            
            return True
            
        except Exception as e:
            log_message(f"模拟爬取失败: {str(e)}", "ERROR")
            return False

def main():
    """主函数"""
    log_message("=" * 60)
    log_message("简化版微博评论爬虫启动（模拟模式）")
    log_message(f"目标微博数: {config.MAX_WEIBO_COUNT}")
    log_message(f"每条微博最大评论数: {config.MAX_COMMENTS_PER_WEIBO}")
    log_message(f"目标总评论数: {config.TARGET_TOTAL_COMMENTS}")
    log_message("=" * 60)
    
    spider = SimpleWeiboSpider()
    success = spider.run_simulation()
    
    if success:
        log_message("=" * 60)
        log_message("模拟爬取任务完成！")
        log_message(f"数据保存在: {config.OUTPUT_DIR} 目录")
        log_message("=" * 60)
        
        stats = spider.get_statistics()
        print("\n📊 爬取统计:")
        print(f"  • 微博数量: {stats['total_weibo']}")
        print(f"  • 评论数量: {stats['total_comments']}")
        print(f"  • 平均每条微博评论数: {stats['avg_comments_per_weibo']:.1f}")
    else:
        log_message("模拟爬取任务失败！", "ERROR")

if __name__ == "__main__":
    main()
