# -*- coding: utf-8 -*-
"""
自动安装依赖包脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🚀 开始安装微博爬虫依赖包...")
    print("=" * 50)
    
    # 基础必需包
    required_packages = [
        "requests",
    ]
    
    # 可选包（用于完整功能）
    optional_packages = [
        "beautifulsoup4",
        "selenium", 
        "pandas",
        "fake-useragent",
        "webdriver-manager",
        "openpyxl",
        "tqdm",
        "lxml"
    ]
    
    # 安装基础包
    print("📦 安装基础必需包...")
    success_count = 0
    for package in required_packages:
        if check_package(package.split('==')[0]):
            print(f"✅ {package} 已安装")
            success_count += 1
        else:
            if install_package(package):
                success_count += 1
    
    print(f"\n基础包安装结果: {success_count}/{len(required_packages)} 成功")
    
    # 安装可选包
    print("\n📦 安装可选功能包...")
    optional_success = 0
    for package in optional_packages:
        package_name = package.split('==')[0]
        if package_name == "beautifulsoup4":
            check_name = "bs4"
        else:
            check_name = package_name.replace('-', '_')
            
        if check_package(check_name):
            print(f"✅ {package} 已安装")
            optional_success += 1
        else:
            if install_package(package):
                optional_success += 1
    
    print(f"\n可选包安装结果: {optional_success}/{len(optional_packages)} 成功")
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 安装总结:")
    print(f"  • 基础包: {success_count}/{len(required_packages)} 成功")
    print(f"  • 可选包: {optional_success}/{len(optional_packages)} 成功")
    
    if success_count == len(required_packages):
        print("\n🎉 基础功能可用！可以运行简化版爬虫:")
        print("   python simple_spider.py")
        
        if optional_success >= 6:  # 大部分可选包安装成功
            print("\n🎉 完整功能可用！可以运行完整版爬虫:")
            print("   python main.py")
        else:
            print("\n⚠️  部分可选包未安装，建议使用简化版爬虫")
    else:
        print("\n❌ 基础包安装不完整，请手动安装:")
        for package in required_packages:
            if not check_package(package.split('==')[0]):
                print(f"   pip install {package}")
    
    print("\n📞 如有问题，请检查网络连接或手动安装依赖包")

if __name__ == "__main__":
    main()
