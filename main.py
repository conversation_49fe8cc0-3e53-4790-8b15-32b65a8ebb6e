# -*- coding: utf-8 -*-
"""
微博评论爬虫主程序
"""

import sys
import argparse
from datetime import datetime
import config
from weibo_spider import <PERSON><PERSON><PERSON>pid<PERSON>
from utils import log_message

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微博评论爬虫')
    parser.add_argument('--headless', action='store_true', default=True,
                        help='是否使用无头浏览器模式 (默认: True)')
    parser.add_argument('--max-weibo', type=int, default=config.MAX_WEIBO_COUNT,
                        help=f'最大微博数量 (默认: {config.MAX_WEIBO_COUNT})')
    parser.add_argument('--max-comments', type=int, default=config.MAX_COMMENTS_PER_WEIBO,
                        help=f'每条微博最大评论数 (默认: {config.MAX_COMMENTS_PER_WEIBO})')
    parser.add_argument('--target-total', type=int, default=config.TARGET_TOTAL_COMMENTS,
                        help=f'目标总评论数 (默认: {config.TARGET_TOTAL_COMMENTS})')
    parser.add_argument('--url', type=str, default=config.WEIBO_SEARCH_URL,
                        help='微博搜索URL')
    
    args = parser.parse_args()
    
    # 更新配置
    config.MAX_WEIBO_COUNT = args.max_weibo
    config.MAX_COMMENTS_PER_WEIBO = args.max_comments
    config.TARGET_TOTAL_COMMENTS = args.target_total
    config.WEIBO_SEARCH_URL = args.url
    
    log_message("=" * 60)
    log_message("微博评论爬虫启动")
    log_message(f"目标URL: {config.WEIBO_SEARCH_URL}")
    log_message(f"最大微博数: {config.MAX_WEIBO_COUNT}")
    log_message(f"每条微博最大评论数: {config.MAX_COMMENTS_PER_WEIBO}")
    log_message(f"目标总评论数: {config.TARGET_TOTAL_COMMENTS}")
    log_message(f"无头模式: {args.headless}")
    log_message("=" * 60)
    
    try:
        # 创建爬虫实例
        spider = WeiboSpider(headless=args.headless)
        
        # 开始爬取
        start_time = datetime.now()
        success = spider.crawl_all()
        end_time = datetime.now()
        
        # 计算耗时
        duration = end_time - start_time
        
        if success:
            log_message("=" * 60)
            log_message("爬取任务完成！")
            log_message(f"总耗时: {duration}")
            log_message(f"数据保存在: {config.OUTPUT_DIR} 目录")
            log_message("=" * 60)
            
            # 显示统计信息
            stats = spider.data_processor.get_statistics()
            print("\n📊 爬取统计:")
            print(f"  • 微博数量: {stats['total_weibo']}")
            print(f"  • 评论数量: {stats['total_comments']}")
            print(f"  • 平均每条微博评论数: {stats['avg_comments_per_weibo']:.1f}")
            print(f"  • 爬取耗时: {duration}")
            
        else:
            log_message("爬取任务失败！", "ERROR")
            sys.exit(1)
            
    except KeyboardInterrupt:
        log_message("用户中断爬取", "WARNING")
        sys.exit(0)
    except Exception as e:
        log_message(f"程序异常: {str(e)}", "ERROR")
        sys.exit(1)

def test_spider():
    """测试爬虫功能"""
    log_message("开始测试爬虫功能...")
    
    # 设置测试参数
    config.MAX_WEIBO_COUNT = 2
    config.MAX_COMMENTS_PER_WEIBO = 10
    config.TARGET_TOTAL_COMMENTS = 20
    
    try:
        spider = WeiboSpider(headless=True)
        success = spider.crawl_all()
        
        if success:
            log_message("测试成功！")
            stats = spider.data_processor.get_statistics()
            print(f"测试结果: {stats}")
        else:
            log_message("测试失败！", "ERROR")
            
    except Exception as e:
        log_message(f"测试异常: {str(e)}", "ERROR")

def show_help():
    """显示帮助信息"""
    help_text = """
🕷️  微博评论爬虫使用指南

📋 功能说明:
   本爬虫用于爬取微博"重庆公交坠江事故"相关评论数据
   
🎯 爬取目标:
   • 爬取前20条热门微博
   • 每条微博爬取300条评论
   • 总目标数据量: 5000条评论

📁 输出文件:
   • CSV格式: weibo_comments_YYYYMMDD_HHMMSS.csv
   • JSON格式: weibo_comments_YYYYMMDD_HHMMSS.json  
   • Excel格式: weibo_comments_YYYYMMDD_HHMMSS.xlsx

🚀 使用方法:
   python main.py                    # 使用默认参数运行
   python main.py --headless         # 无头模式运行
   python main.py --max-weibo 10     # 设置最大微博数
   python main.py --max-comments 200 # 设置每条微博最大评论数
   
🔧 参数说明:
   --headless        是否使用无头浏览器模式
   --max-weibo       最大微博数量
   --max-comments    每条微博最大评论数
   --target-total    目标总评论数
   --url            自定义微博搜索URL

⚠️  注意事项:
   1. 首次运行会自动下载ChromeDriver
   2. 请确保网络连接稳定
   3. 爬取过程中请勿关闭程序
   4. 数据将保存在data目录下
   
📞 如有问题，请检查网络连接和依赖包安装
"""
    print(help_text)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_spider()
    elif len(sys.argv) > 1 and sys.argv[1] == 'help':
        show_help()
    else:
        main()
