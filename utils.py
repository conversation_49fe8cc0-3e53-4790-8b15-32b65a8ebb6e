# -*- coding: utf-8 -*-
"""
工具函数模块
"""

import time
import random
import re
import os
from datetime import datetime, timedelta
from fake_useragent import UserAgent
import config

def get_random_user_agent():
    """获取随机User-Agent"""
    try:
        ua = UserAgent()
        return ua.random
    except:
        return random.choice(config.USER_AGENTS)

def random_delay(min_delay=None, max_delay=None):
    """随机延时"""
    min_delay = min_delay or config.MIN_DELAY
    max_delay = max_delay or config.MAX_DELAY
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def clean_text(text):
    """清理文本内容"""
    if not text:
        return ""

    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())

    # 移除特殊字符
    text = re.sub(r'[\r\n\t]', ' ', text)

    # 移除emoji（可选）
    # text = re.sub(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]', '', text)

    return text.strip()

def extract_number(text):
    """从文本中提取数字"""
    if not text:
        return 0

    # 处理万、千等单位
    text = str(text).lower()
    if '万' in text:
        number = re.findall(r'(\d+\.?\d*)', text)
        if number:
            return int(float(number[0]) * 10000)
    elif '千' in text:
        number = re.findall(r'(\d+\.?\d*)', text)
        if number:
            return int(float(number[0]) * 1000)
    else:
        number = re.findall(r'\d+', text)
        if number:
            return int(number[0])

    return 0

def format_time(time_str):
    """格式化时间字符串"""
    if not time_str:
        return ""

    try:
        # 处理相对时间
        if '分钟前' in time_str:
            minutes = extract_number(time_str)
            return (datetime.now() - timedelta(minutes=minutes)).strftime('%Y-%m-%d %H:%M:%S')
        elif '小时前' in time_str:
            hours = extract_number(time_str)
            return (datetime.now() - timedelta(hours=hours)).strftime('%Y-%m-%d %H:%M:%S')
        elif '天前' in time_str:
            days = extract_number(time_str)
            return (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')
        elif '今天' in time_str:
            time_part = re.findall(r'(\d{2}:\d{2})', time_str)
            if time_part:
                return datetime.now().strftime('%Y-%m-%d') + ' ' + time_part[0] + ':00'
        elif '昨天' in time_str:
            time_part = re.findall(r'(\d{2}:\d{2})', time_str)
            if time_part:
                yesterday = datetime.now() - timedelta(days=1)
                return yesterday.strftime('%Y-%m-%d') + ' ' + time_part[0] + ':00'
        else:
            # 尝试直接解析
            return time_str
    except:
        return time_str

def ensure_dir(directory):
    """确保目录存在"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def generate_filename(base_name, extension, timestamp=True):
    """生成带时间戳的文件名"""
    if timestamp:
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        name, ext = os.path.splitext(base_name)
        return f"{name}_{timestamp_str}.{extension}"
    else:
        return f"{base_name}.{extension}"

def log_message(message, level="INFO"):
    """记录日志消息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

def extract_weibo_id(url):
    """从URL中提取微博ID"""
    if not url:
        return ""

    # 匹配微博ID的正则表达式
    patterns = [
        r'/(\d+)\?',
        r'/(\w+)$',
        r'id=(\w+)',
        r'/detail/(\w+)'
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    return ""

def is_valid_weibo_content(content):
    """验证微博内容是否有效"""
    if not content or len(content.strip()) < 5:
        return False

    # 过滤广告或无效内容
    invalid_keywords = ['广告', '推广', '营销', '代购']
    content_lower = content.lower()

    for keyword in invalid_keywords:
        if keyword in content_lower:
            return False

    return True
